<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义下拉组件测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* ========== 自定义下拉组件样式 ========== */
        /* 自定义下拉组件容器 */
        .custom-select-container {
          position: relative;
          width: 100%;
          display: inline-block;
        }

        /* 自定义下拉框主体 */
        .custom-select {
          width: 100%;
          padding: 12px 40px 12px 16px;
          border: 2px solid #e8e9ea;
          border-radius: 8px;
          background-color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          color: #333;
          cursor: pointer;
          user-select: none;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          min-height: 44px;
          box-sizing: border-box;
        }

        /* 下拉箭头 */
        .custom-select::after {
          content: '';
          width: 16px;
          height: 16px;
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          transition: transform 0.3s ease;
          flex-shrink: 0;
        }

        /* 悬停和聚焦状态 */
        .custom-select:hover,
        .custom-select:focus {
          border-color: #40a9ff;
          background-color: #fafbfc;
          outline: none;
        }

        .custom-select:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
        }

        /* 展开状态的箭头 */
        .custom-select.open::after {
          transform: rotate(180deg);
        }

        /* 下拉选项列表 */
        .custom-select-options {
          position: absolute;
          top: 100%;
          left: 0;
          width: 160px; /* 设置选项列表宽度为160px，比下拉框本身窄 */
          max-height: 300px;
          overflow-y: auto;
          background: #ffffff;
          border: 2px solid #e8e9ea;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          z-index: 1000;
          display: none;
          margin-top: 4px;
        }

        /* 显示选项列表 */
        .custom-select-options.show {
          display: block;
        }

        /* 选项项目 */
        .custom-select-option {
          padding: 10px 16px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          font-size: 14px;
          color: #333;
          border-bottom: 1px solid #f0f0f0;
        }

        .custom-select-option:last-child {
          border-bottom: none;
        }

        .custom-select-option:hover {
          background-color: #f5f5f5;
        }

        .custom-select-option.selected {
          background-color: #e6f7ff;
          color: #1890ff;
          font-weight: 500;
        }

        .container {
          max-width: 600px;
          margin: 50px auto;
        }

        .demo-section {
          margin-bottom: 30px;
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>自定义下拉组件测试</h1>
        
        <div class="demo-section">
            <h3>分析类型选择</h3>
            <p>下拉框本身保持原宽度，但弹出的选项列表宽度为160px</p>
            
            <div class="custom-select-container">
                <div class="custom-select" id="analysisType" tabindex="0">
                    <span class="custom-select-text">信度分析</span>
                </div>
                <div class="custom-select-options" id="analysisTypeOptions">
                    <div class="custom-select-option selected" data-value="cronbach">信度分析</div>
                    <div class="custom-select-option" data-value="factor">效度分析</div>
                    <div class="custom-select-option" data-value="frequency">频数统计</div>
                    <div class="custom-select-option" data-value="desc">描述性统计</div>
                    <div class="custom-select-option" data-value="correlation">相关分析</div>
                    <div class="custom-select-option" data-value="regression">线性回归</div>
                    <div class="custom-select-option" data-value="anova">方差分析</div>
                    <div class="custom-select-option" data-value="independentTTest">独立样本t检验</div>
                    <div class="custom-select-option" data-value="oneSampleTTest">单样本t检验</div>
                    <div class="custom-select-option" data-value="moderation">调节效应</div>
                    <div class="custom-select-option" data-value="mediation">中介效应</div>
                    <div class="custom-select-option" data-value="crossTab">交叉(卡方)分析</div>
                </div>
                <input type="hidden" name="analysisType" id="analysisTypeInput" value="cronbach">
            </div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="showCurrentValue()">显示当前值</button>
                <button class="btn btn-secondary" onclick="setValue('correlation')">设置为相关分析</button>
            </div>
            
            <div id="currentValue" class="mt-2"></div>
        </div>
    </div>

    <script>
        // 自定义下拉组件功能
        function initCustomSelect() {
            const customSelect = document.getElementById('analysisType');
            const optionsContainer = document.getElementById('analysisTypeOptions');
            const hiddenInput = document.getElementById('analysisTypeInput');
            const selectText = customSelect.querySelector('.custom-select-text');
            
            if (!customSelect || !optionsContainer || !hiddenInput || !selectText) {
                return;
            }
            
            // 点击下拉框主体
            customSelect.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleDropdown();
            });
            
            // 键盘支持
            customSelect.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleDropdown();
                } else if (e.key === 'Escape') {
                    closeDropdown();
                }
            });
            
            // 点击选项
            optionsContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('custom-select-option')) {
                    selectOption(e.target);
                }
            });
            
            // 点击外部关闭下拉框
            document.addEventListener('click', (e) => {
                if (!customSelect.contains(e.target) && !optionsContainer.contains(e.target)) {
                    closeDropdown();
                }
            });
            
            function toggleDropdown() {
                const isOpen = optionsContainer.classList.contains('show');
                if (isOpen) {
                    closeDropdown();
                } else {
                    openDropdown();
                }
            }
            
            function openDropdown() {
                optionsContainer.classList.add('show');
                customSelect.classList.add('open');
                customSelect.setAttribute('aria-expanded', 'true');
            }
            
            function closeDropdown() {
                optionsContainer.classList.remove('show');
                customSelect.classList.remove('open');
                customSelect.setAttribute('aria-expanded', 'false');
            }
            
            function selectOption(optionElement) {
                // 移除之前的选中状态
                optionsContainer.querySelectorAll('.custom-select-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // 设置新的选中状态
                optionElement.classList.add('selected');
                
                // 更新显示文本
                selectText.textContent = optionElement.textContent;
                
                // 更新隐藏input的值
                hiddenInput.value = optionElement.getAttribute('data-value');
                
                // 关闭下拉框
                closeDropdown();
                
                // 触发change事件
                const changeEvent = new Event('change', { bubbles: true });
                customSelect.dispatchEvent(changeEvent);
            }
            
            // 添加获取当前值的方法
            customSelect.getValue = function() {
                return hiddenInput.value;
            };
            
            // 添加设置值的方法
            customSelect.setValue = function(value) {
                const option = optionsContainer.querySelector(`[data-value="${value}"]`);
                if (option) {
                    selectOption(option);
                }
            };
        }

        // 测试函数
        function showCurrentValue() {
            const customSelect = document.getElementById('analysisType');
            const value = customSelect.getValue();
            document.getElementById('currentValue').innerHTML = `<strong>当前值:</strong> ${value}`;
        }

        function setValue(value) {
            const customSelect = document.getElementById('analysisType');
            customSelect.setValue(value);
            showCurrentValue();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initCustomSelect();
            showCurrentValue();
        });
    </script>
</body>
</html>
